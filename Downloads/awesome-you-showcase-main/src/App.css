
#root {
  max-width: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
}

.glass-effect {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* Enhanced card hover effect */
.enhanced-card {
  transition: transform 0.3s, box-shadow 0.3s;
}

.enhanced-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #8B5CF6;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #7C3AED;
}

/* Pattern background */
.pattern-dots {
  background-image: radial-gradient(#4F46E5 0.5px, transparent 0.5px);
  background-size: 15px 15px;
}

.pattern-grid {
  background-size: 20px 20px;
  background-image: 
    linear-gradient(to right, rgba(79, 70, 229, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(79, 70, 229, 0.1) 1px, transparent 1px);
}

/* Reveal animations */
@keyframes reveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.reveal {
  animation: reveal 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  opacity: 0;
}

.reveal-delay-1 {
  animation-delay: 0.1s;
}

.reveal-delay-2 {
  animation-delay: 0.2s;
}

.reveal-delay-3 {
  animation-delay: 0.3s;
}
