
import { useState, useRef, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Github, ExternalLink } from "lucide-react";

const projects = [
  {
    id: 1,
    title: "Multimodal RAG Approach",
    category: "AI/ML",
    image: "https://images.unsplash.com/photo-1591696331111-ef9586a5b17a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
    description: "Built a multimodal RAG system using FAISS and OpenAI LLMs, testing both standard and ColPali-based retrieval with improved document understanding with Qwen2-VL and deployed with MMR re-ranking via Google Cloud Run.",
    technologies: ["Python", "OpenAI", "FAISS", "Streamlit", "Whisper", "Tor<PERSON>udi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>wen2-VL", "Docker", "G<PERSON>"],
    liveUrl: "#",
    gitUrl: "https://github.com/Nanibucky/Multimodal-RAG"
  },
  {
    id: 2,
    title: "Fine-tuned Mistral 7B for Text-to-SQL",
    category: "NLP/LLM",
    image: "https://images.unsplash.com/photo-1633409361618-c73427e4e206?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
    description: "Fine-tuned Mistral 7B for natural language to SQL conversion, achieving 80% accuracy and a 40% reduction in query time. Published a quantized GGUF model (Q8_0) on Hugging Face, Ollama for lightweight deployments.",
    technologies: ["Python", "Hugging Face", "LoRA", "PEFT", "Llama.cpp", "Ollama", "Quantization"],
    liveUrl: "https://huggingface.co/Nanibucky/Mistral-7B-SQL",
    gitUrl: "https://github.com/Nanibucky/Text-to-SQL-LLM"
  },
  {
    id: 3,
    title: "AI-Powered Chatbot with AWS Bedrock",
    category: "NLP/Cloud",
    image: "https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
    description: "Built a chatbot with AWS Bedrock's foundation models, enhancing natural language understanding and response accuracy. Developed a FastAPI backend for efficient API handling and integrated multi-session support.",
    technologies: ["Python", "FastAPI", "Streamlit", "AWS Bedrock"],
    liveUrl: "#",
    gitUrl: "https://github.com/Nanibucky/AWS-Bedrock-Chatbot"
  },
  {
    id: 4,
    title: "Memory-Enabled Task Management Agent",
    category: "AI Agent",
    image: "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
    description: "Built an AI agent with long-term memory for task management with retrieval accuracy by 85% and reducing redundant memory storage by 40%. Implemented dynamic memory selection for tasks storage.",
    technologies: ["Python", "LangGraph", "Trustcall", "LLMs", "Semantic Memory", "Agents"],
    liveUrl: "#",
    gitUrl: "https://github.com/Nanibucky/Memory-Agent"
  },
  {
    id: 5,
    title: "Prompt Enhancer",
    category: "AI Tools",
    image: "https://images.unsplash.com/photo-1487058792275-0ad4aaf24ca7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
    description: "Developed a tool that enhances prompts for AI models, improving output quality by analyzing and restructuring user inputs for better contextual understanding and more accurate responses.",
    technologies: ["Python", "OpenAI API", "NLP", "Streamlit", "LangChain", "Prompt Engineering"],
    liveUrl: "#",
    gitUrl: "https://github.com/Nanibucky/prompt-enhancer"
  },
  {
    id: 6,
    title: "Reporter AI",
    category: "AI/Automation",
    image: "https://images.unsplash.com/photo-1508780709619-79562169bc64?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
    description: "Created an automated reporting system that uses AI to generate comprehensive reports from raw data, simplifying the analysis process and reducing manual effort while ensuring consistency and accuracy.",
    technologies: ["Python", "Data Analysis", "LLMs", "Automated Reporting", "Pandas", "Document Generation"],
    liveUrl: "#",
    gitUrl: "https://github.com/Nanibucky/reporter-ai"
  }
];

const ProjectsSection = () => {
  const [activeFilter, setActiveFilter] = useState("All");
  const sectionRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.2 }
    );
    
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    
    return () => observer.disconnect();
  }, []);

  const categories = ["All", ...Array.from(new Set(projects.map((project) => project.category)))];

  const filteredProjects = activeFilter === "All"
    ? projects
    : projects.filter((project) => project.category === activeFilter);

  return (
    <section 
      id="projects" 
      ref={sectionRef}
      className="py-20 md:py-32 bg-white"
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16 opacity-0 animate-fade-in [animation-delay:200ms]">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-portfolio-slate">My Projects</h2>
          <div className="w-20 h-1 bg-portfolio-blue mb-6 mx-auto"></div>
          <p className="text-lg text-portfolio-slate/80 max-w-2xl mx-auto">
            Here are some of my recent projects. Each project demonstrates my skills in 
            data analysis, machine learning, and AI development.
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-3 mb-12 opacity-0 animate-fade-in [animation-delay:400ms]">
          {categories.map((category) => (
            <Button
              key={category}
              onClick={() => setActiveFilter(category)}
              variant={activeFilter === category ? "default" : "outline"}
              className={`rounded-full px-6 ${
                activeFilter === category 
                  ? "bg-portfolio-blue hover:bg-portfolio-darkBlue" 
                  : "text-portfolio-slate hover:text-portfolio-blue"
              }`}
            >
              {category}
            </Button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <ProjectCard 
              key={project.id} 
              project={project} 
              isVisible={isVisible}
              delay={(index + 1) * 200}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

interface ProjectCardProps {
  project: {
    id: number;
    title: string;
    category: string;
    image: string;
    description: string;
    technologies: string[];
    liveUrl: string;
    gitUrl: string;
  };
  isVisible: boolean;
  delay: number;
}

const ProjectCard = ({ project, isVisible, delay }: ProjectCardProps) => {
  return (
    <Card 
      className={`border-portfolio-blue/20 overflow-hidden shadow-md hover:shadow-lg transition-all opacity-0 ${
        isVisible ? "animate-fade-in" : ""
      }`}
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className="relative h-48 overflow-hidden">
        <img 
          src={project.image} 
          alt={project.title} 
          className="w-full h-full object-cover transition-transform hover:scale-105 duration-500"
        />
        <div className="absolute top-4 right-4 bg-portfolio-blue text-white text-xs font-medium px-2 py-1 rounded">
          {project.category}
        </div>
      </div>
      
      <CardContent className="p-6">
        <h3 className="font-bold text-xl mb-2 text-portfolio-slate">{project.title}</h3>
        <p className="text-portfolio-slate/80 mb-4 line-clamp-2">{project.description}</p>
        
        <div className="flex flex-wrap gap-2 mb-6">
          {project.technologies.map((tech) => (
            <span 
              key={tech} 
              className="bg-portfolio-gray text-portfolio-slate/80 text-xs px-2 py-1 rounded"
            >
              {tech}
            </span>
          ))}
        </div>
        
        <div className="flex justify-between">
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <ExternalLink className="h-4 w-4" />
            <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">Live Demo</a>
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Github className="h-4 w-4" />
            <a href={project.gitUrl} target="_blank" rel="noopener noreferrer">Code</a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProjectsSection;
