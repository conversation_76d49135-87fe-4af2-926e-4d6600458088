import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Mail, MessageSquare, Phone, Send } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import emailjs from '@emailjs/browser';

const ContactSection = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // EmailJS configuration
    const SERVICE_ID = 'service_367zw3k';
    const TEMPLATE_ID = 'template_1595lqg';
    const PUBLIC_KEY = 'M2TJWZpj65ocXnH5_';

    // Validate form data
    if (!formData.name.trim() || !formData.email.trim() || !formData.message.trim()) {
      toast({
        title: "Please fill in all required fields",
        description: "Name, email, and message are required.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Initialize EmailJS
      emailjs.init(PUBLIC_KEY);

      // Template parameters that match your EmailJS template
      const templateParams = {
        name: formData.name,
        email: formData.email,
        title: formData.subject || 'Portfolio Contact',
        message: formData.message,
      };

      console.log('Sending email with params:', templateParams);

      const response = await emailjs.send(
        SERVICE_ID,
        TEMPLATE_ID,
        templateParams,
        PUBLIC_KEY
      );

      console.log('EmailJS response:', response);

      toast({
        title: "Message sent successfully!",
        description: "Thank you for your message. I'll get back to you soon.",
      });

      setFormData({
        name: "",
        email: "",
        subject: "",
        message: "",
      });
    } catch (error: any) {
      console.error('EmailJS error:', error);

      let errorMessage = "Please try again or contact me directly via email.";

      if (error?.text) {
        errorMessage = `Error: ${error.text}`;
      } else if (error?.message) {
        errorMessage = `Error: ${error.message}`;
      }

      toast({
        title: "Failed to send message",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-20 md:py-32 bg-portfolio-gray">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16 opacity-0 animate-fade-in [animation-delay:200ms]">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-portfolio-slate">Get In Touch</h2>
          <div className="w-20 h-1 bg-portfolio-blue mb-6 mx-auto"></div>
          <p className="text-lg text-portfolio-slate/80 max-w-2xl mx-auto">
            Have a project in mind or want to discuss potential opportunities?
            Feel free to reach out using the contact form or direct contact information below.
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-10">
          <div className="w-full lg:w-1/3 opacity-0 animate-fade-in [animation-delay:300ms]">
            <Card className="h-full border-portfolio-blue/20">
              <CardContent className="p-6 flex flex-col h-full">
                <h3 className="font-bold text-xl mb-8 text-portfolio-slate">Contact Information</h3>

                <div className="space-y-8 flex-grow">
                  <div className="flex items-start">
                    <div className="bg-portfolio-blue/10 p-3 rounded-full mr-4">
                      <Mail className="h-5 w-5 text-portfolio-blue" />
                    </div>
                    <div>
                      <p className="font-medium text-portfolio-slate">Email</p>
                      <a href="mailto:<EMAIL>" className="text-portfolio-blue hover:underline">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-portfolio-blue/10 p-3 rounded-full mr-4">
                      <Phone className="h-5 w-5 text-portfolio-blue" />
                    </div>
                    <div>
                      <p className="font-medium text-portfolio-slate">Phone</p>
                      <a href="tel:+13134511614" className="text-portfolio-blue hover:underline">
                        +****************
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-portfolio-blue/10 p-3 rounded-full mr-4">
                      <MessageSquare className="h-5 w-5 text-portfolio-blue" />
                    </div>
                    <div>
                      <p className="font-medium text-portfolio-slate">Social Media</p>
                      <div className="flex space-x-4 mt-2">
                        <a href="https://www.linkedin.com/in/reddyint-reddy" className="text-portfolio-slate hover:text-portfolio-blue transition-colors">
                          LinkedIn
                        </a>
                        <a href="https://github.com/Nanibucky" className="text-portfolio-slate hover:text-portfolio-blue transition-colors">
                          GitHub
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="w-full lg:w-2/3 opacity-0 animate-fade-in [animation-delay:500ms]">
            <Card className="border-portfolio-blue/20">
              <CardContent className="p-6">
                <h3 className="font-bold text-xl mb-8 text-portfolio-slate">Send Me a Message</h3>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block mb-2 text-sm font-medium text-portfolio-slate">
                        Your Name
                      </label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="John Doe"
                        required
                        className="border-portfolio-blue/20 focus:border-portfolio-blue focus:ring focus:ring-portfolio-blue/20"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block mb-2 text-sm font-medium text-portfolio-slate">
                        Your Email
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="<EMAIL>"
                        required
                        className="border-portfolio-blue/20 focus:border-portfolio-blue focus:ring focus:ring-portfolio-blue/20"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block mb-2 text-sm font-medium text-portfolio-slate">
                      Subject
                    </label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      placeholder="Project Inquiry"
                      required
                      className="border-portfolio-blue/20 focus:border-portfolio-blue focus:ring focus:ring-portfolio-blue/20"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="block mb-2 text-sm font-medium text-portfolio-slate">
                      Your Message
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      placeholder="Hello, I'm interested in discussing a potential opportunity..."
                      required
                      rows={5}
                      className="border-portfolio-blue/20 focus:border-portfolio-blue focus:ring focus:ring-portfolio-blue/20 resize-none"
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-portfolio-blue hover:bg-portfolio-darkBlue text-white rounded-full px-6 py-5"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {isSubmitting ? "Sending..." : "Send Message"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
