
import { <PERSON>, CardContent } from "@/components/ui/card";
import { G<PERSON><PERSON>, <PERSON>ed<PERSON> } from "lucide-react";

const AboutSection = () => {
  return (
    <section id="about" className="py-20 md:py-32 bg-white">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-start gap-12">
          <div className="w-full lg:w-1/2 opacity-0 animate-fade-in [animation-delay:200ms]">
            <h2 className="text-3xl md:text-4xl font-bold mb-2 text-portfolio-slate">About Me</h2>
            <div className="w-20 h-1 bg-portfolio-blue mb-8"></div>
            
            <p className="text-lg text-portfolio-slate/80 mb-6">
              I'm a passionate <span className="font-semibold text-portfolio-blue">Data Analyst & AI Engineer</span> with over 2+ years of experience specializing in 
              cutting-edge <span className="font-semibold text-portfolio-accent">Generative AI</span> and machine learning solutions across finance, healthcare, and tech sectors.
            </p>
            
            <p className="text-lg text-portfolio-slate/80 mb-6">
              My expertise spans <span className="font-semibold text-portfolio-blue">fine-tuning large language models (LLMs)</span>, building 
              <span className="font-semibold text-portfolio-accent"> multimodal RAG systems</span>, and developing AI agents with long-term memory capabilities. 
              I've successfully deployed models using <span className="font-semibold text-portfolio-blue">LoRA, PEFT, and quantization techniques</span>, 
              achieving significant improvements in accuracy and performance.
            </p>
            
            <p className="text-lg text-portfolio-slate/80 mb-6">
              At <span className="font-semibold text-portfolio-blue">JP Morgan Chase & Co.</span>, I leverage 
              <span className="font-semibold text-portfolio-accent"> OpenAI APIs, LangChain, and Transformers</span> to build intelligent automation tools, 
              including AI-powered transaction visualizers and compliance summarization pipelines that have reduced manual review time by 40%.
            </p>
            
            <p className="text-lg text-portfolio-slate/80 mb-6">
              I'm particularly passionate about <span className="font-semibold text-portfolio-accent">prompt engineering, retrieval-augmented generation (RAG)</span>, 
              and creating production-ready AI solutions that bridge the gap between cutting-edge research and real-world business applications.
            </p>

            {/* Social Media Links */}
            <div className="flex items-center space-x-4 mt-8">
              <p className="text-portfolio-slate/80 font-medium">Connect with me:</p>
              <a 
                href="https://www.linkedin.com/in/reddyint-reddy" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-portfolio-blue hover:text-portfolio-darkBlue transition-colors"
              >
                <Linkedin className="w-5 h-5" />
                <span>LinkedIn</span>
              </a>
              <a 
                href="https://github.com/Nanibucky" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-portfolio-slate hover:text-portfolio-blue transition-colors"
              >
                <Github className="w-5 h-5" />
                <span>GitHub</span>
              </a>
            </div>
          </div>
          
          <div className="w-full lg:w-1/2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-portfolio-blue/20 shadow-md hover:shadow-lg transition-shadow opacity-0 animate-fade-in [animation-delay:300ms]">
                <CardContent className="p-6">
                  <h3 className="font-bold text-xl mb-2 text-portfolio-slate">Education</h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-portfolio-slate/80">
                        <strong>MS in Data Science</strong><br />
                        University of Michigan-Dearborn, 2024
                      </p>
                    </div>
                    <div>
                      <p className="text-portfolio-slate/80">
                        <strong>BTech in Computer Science</strong><br />
                        Undergraduate Degree
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="border-portfolio-blue/20 shadow-md hover:shadow-lg transition-shadow opacity-0 animate-fade-in [animation-delay:400ms]">
                <CardContent className="p-6">
                  <h3 className="font-bold text-xl mb-2 text-portfolio-slate">AI/ML Experience</h3>
                  <p className="text-portfolio-slate/80">
                    <strong>2+ Years</strong><br />
                    GenAI & Data Analytics
                  </p>
                </CardContent>
              </Card>
              
              <Card className="border-portfolio-blue/20 shadow-md hover:shadow-lg transition-shadow opacity-0 animate-fade-in [animation-delay:500ms]">
                <CardContent className="p-6">
                  <h3 className="font-bold text-xl mb-2 text-portfolio-slate">GitHub Projects</h3>
                  <p className="text-portfolio-slate/80">
                    <strong>15+ Repositories</strong><br />
                    <a 
                      href="https://github.com/Nanibucky" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-portfolio-blue hover:text-portfolio-darkBlue transition-colors underline"
                    >
                      View on GitHub
                    </a>
                  </p>
                </CardContent>
              </Card>
              
              <Card className="border-portfolio-blue/20 shadow-md hover:shadow-lg transition-shadow opacity-0 animate-fade-in [animation-delay:600ms]">
                <CardContent className="p-6">
                  <h3 className="font-bold text-xl mb-2 text-portfolio-slate">Specialization</h3>
                  <p className="text-portfolio-slate/80">
                    <strong>Generative AI</strong><br />
                    LLM Fine-tuning & RAG
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
