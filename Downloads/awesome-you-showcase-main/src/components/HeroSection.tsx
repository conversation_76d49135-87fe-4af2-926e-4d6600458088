

import { But<PERSON> } from "@/components/ui/button";
import { ArrowDown, Code, Star, Sparkles } from "lucide-react";
import { useEffect, useRef, useState } from "react";

const HeroSection = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const avatarRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    setIsLoaded(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!avatarRef.current) return;
      
      const { left, top, width, height } = avatarRef.current.getBoundingClientRect();
      const centerX = left + width / 2;
      const centerY = top + height / 2;
      
      // Calculate distance from center (0 to 1)
      const distX = (e.clientX - centerX) / (window.innerWidth / 2);
      const distY = (e.clientY - centerY) / (window.innerHeight / 2);
      
      // Apply subtle transform for parallax effect
      avatarRef.current.style.transform = `perspective(1000px) rotateY(${distX * 5}deg) rotateX(${-distY * 5}deg)`;
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const scrollToProjects = () => {
    const projectsSection = document.getElementById('projects');
    if (projectsSection) {
      projectsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center pt-16 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-portfolio-gray to-white opacity-70 -z-10" />
      
      {/* Decorative elements */}
      <div className="absolute top-20 left-20 w-64 h-64 bg-portfolio-gradient1/20 rounded-full blur-3xl animate-pulse-slow"></div>
      <div className="absolute bottom-20 right-20 w-80 h-80 bg-portfolio-gradient2/20 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-portfolio-gradient3/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
      
      <div className="container mx-auto px-6 lg:px-8 py-16 md:py-24">
        <div className="flex flex-col lg:flex-row items-center justify-between">
          <div className={`w-full lg:w-1/2 mb-12 lg:mb-0 opacity-0 ${isLoaded ? 'animate-fade-in [animation-delay:200ms]' : ''}`}>
            <div className="flex items-center mb-3 space-x-2">
              <div className="h-px w-12 bg-portfolio-accent"></div>
              <p className="text-portfolio-accent font-medium">Hello, my name is</p>
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-portfolio-slate">
              Tharun Reddy Pyayala
            </h1>
            
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-medium mb-6 bg-gradient-to-r from-portfolio-blue to-portfolio-accent bg-clip-text text-transparent">
              I'm a <span className="relative">
                Data Analyst 
                <Sparkles className="w-5 h-5 absolute -top-4 -right-4 text-yellow-400" />
              </span> & AI Engineer
            </h2>
            
            <p className="text-lg text-portfolio-slate/70 mb-8 max-w-xl leading-relaxed">
              AI Engineer & GenAI Specialist with 2+ years of experience in machine learning, LLM fine-tuning, and generative AI. 
              Proficient in Python, PyTorch, and advanced AI frameworks, with expertise in building RAG systems, AI agents, and 
              intelligent automation solutions. Passionate about transforming cutting-edge AI research into production-ready applications.
            </p>
            
            <div className="flex flex-wrap gap-4">
              <Button 
                onClick={scrollToProjects}
                className="bg-gradient-to-r from-portfolio-blue to-portfolio-accent hover:from-portfolio-darkBlue hover:to-portfolio-accent/90 text-white rounded-full px-8 py-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Code className="mr-2 h-4 w-4" /> View My Work
              </Button>
              <Button 
                variant="outline" 
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="border-portfolio-blue text-portfolio-blue hover:bg-portfolio-blue/10 rounded-full px-8 py-6"
              >
                Contact Me
              </Button>
            </div>
            
            <div className="mt-8 flex items-center space-x-3">
              <div className="flex -space-x-2">
                <div className="w-8 h-8 rounded-full bg-blue-400 flex items-center justify-center text-xs text-white">JP</div>
                <div className="w-8 h-8 rounded-full bg-purple-400 flex items-center justify-center text-xs text-white">DE</div>
                <div className="w-8 h-8 rounded-full bg-pink-400 flex items-center justify-center text-xs text-white">UM</div>
              </div>
              <p className="text-sm text-portfolio-slate/60">
                Experience at <span className="font-semibold">JP Morgan & Dell</span>
              </p>
            </div>
          </div>

          <div className={`w-full lg:w-1/2 flex justify-center lg:justify-end opacity-0 ${isLoaded ? 'animate-fade-in [animation-delay:400ms]' : ''}`}>
            <div 
              ref={avatarRef}
              className="relative w-64 h-64 md:w-80 md:h-80 transition-all duration-300"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-portfolio-blue to-portfolio-pink rounded-full blur-xl opacity-30 animate-pulse-slow"></div>
              <div className="absolute inset-6 bg-gradient-to-br from-portfolio-gradient1 via-portfolio-gradient2 to-portfolio-gradient3 rounded-full animate-rotate-slow">
                <div className="absolute inset-0.5 bg-white rounded-full"></div>
              </div>
              <div className="absolute inset-10 bg-gradient-to-br from-portfolio-blue/5 to-portfolio-pink/5 rounded-full flex items-center justify-center overflow-hidden border-2 border-white">
                <div className="w-full h-full bg-gradient-to-br from-portfolio-blue/10 to-portfolio-pink/10 flex items-center justify-center">
                  <span className="font-mono text-portfolio-slate text-4xl font-bold">TR</span>
                </div>
                
                {/* Decorative elements inside avatar */}
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-portfolio-accent rounded-full flex items-center justify-center text-white">
                  <Star className="w-3 h-3" />
                </div>
                
                <div className="absolute -bottom-1 -left-1 w-5 h-5 bg-portfolio-blue rounded-full"></div>
                <div className="absolute top-1/3 right-0 w-3 h-3 bg-portfolio-pink rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 ${isLoaded ? 'animate-fade-in [animation-delay:800ms]' : ''}`}>
          <Button
            variant="ghost"
            onClick={() => document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })}
            className="rounded-full flex items-center justify-center animate-float"
          >
            <ArrowDown className="h-6 w-6 text-portfolio-slate" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;

