
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useRef, useState, useEffect } from "react";

const SkillsSection = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.2 }
    );
    
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    
    return () => observer.disconnect();
  }, []);

  const programmingSkills = [
    { name: "Python", value: 95 },
    { name: "SQL", value: 90 },
    { name: "R", value: 85 },
    { name: "C", value: 75 },
  ];

  const dataAnalysisSkills = [
    { name: "Pandas/NumPy", value: 92 },
    { name: "Scikit-learn", value: 88 },
    { name: "TensorFlow/Keras", value: 85 },
    { name: "Tableau/Power BI", value: 90 },
    { name: "ETL Workflows", value: 85 },
  ];

  const cloudSkills = [
    { name: "AWS", value: 80 },
    { name: "GCP", value: 85 },
    { name: "Azure", value: 75 },
    { name: "Docker", value: 78 },
    { name: "Git/GitHub", value: 90 },
  ];

  return (
    <section 
      id="skills" 
      ref={sectionRef}
      className="py-20 md:py-32 bg-portfolio-gray"
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16 opacity-0 animate-fade-in [animation-delay:200ms]">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-portfolio-slate">My Skills</h2>
          <div className="w-20 h-1 bg-portfolio-blue mb-6 mx-auto"></div>
          <p className="text-lg text-portfolio-slate/80 max-w-2xl mx-auto">
            I've developed expertise in various technologies and tools throughout my career.
            Here's an overview of my technical proficiencies.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <SkillCard 
            title="Programming" 
            skills={programmingSkills} 
            isVisible={isVisible} 
            delay={300} 
          />
          <SkillCard 
            title="Data Analysis & ML" 
            skills={dataAnalysisSkills} 
            isVisible={isVisible} 
            delay={500} 
          />
          <SkillCard 
            title="Cloud & Tools" 
            skills={cloudSkills} 
            isVisible={isVisible} 
            delay={700} 
          />
        </div>
      </div>
    </section>
  );
};

interface SkillCardProps {
  title: string;
  skills: { name: string; value: number }[];
  isVisible: boolean;
  delay: number;
}

const SkillCard = ({ title, skills, isVisible, delay }: SkillCardProps) => {
  return (
    <Card className={`border-portfolio-blue/20 shadow-md opacity-0 ${isVisible ? 'animate-fade-in' : ''}`} style={{ animationDelay: `${delay}ms` }}>
      <CardContent className="p-6">
        <h3 className="font-bold text-xl mb-6 text-portfolio-slate">{title}</h3>
        <div className="space-y-6">
          {skills.map((skill) => (
            <div key={skill.name}>
              <div className="flex justify-between mb-2">
                <span className="font-medium text-portfolio-slate">{skill.name}</span>
                <span className="text-portfolio-blue">{skill.value}%</span>
              </div>
              <Progress value={isVisible ? skill.value : 0} className="h-2 transition-all duration-1000" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default SkillsSection;
