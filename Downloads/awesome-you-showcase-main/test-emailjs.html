<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmailJS Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
</head>
<body>
    <h1>EmailJS Configuration Test</h1>
    <div id="result"></div>

    <script>
        // Your current EmailJS configuration
        const SERVICE_ID = 'service_367zw3k';
        const TEMPLATE_ID = 'template_1595lqg';
        const PUBLIC_KEY = 'M2TJWZpj65ocXnH5_';

        const resultDiv = document.getElementById('result');

        function log(message, type = 'info') {
            const p = document.createElement('p');
            p.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            p.textContent = message;
            resultDiv.appendChild(p);
            console.log(message);
        }

        async function testEmailJS() {
            log('🔧 Testing EmailJS Configuration...');

            try {
                // Initialize EmailJS
                emailjs.init(PUBLIC_KEY);
                log('✅ EmailJS initialized successfully');

                // Test template parameters (matching your EmailJS template)
                const templateParams = {
                    name: 'Test User',
                    from_email: '<EMAIL>',
                    title: 'Test Subject',
                    message: 'This is a test message from the portfolio contact form.',
                    reply_to: '<EMAIL>',
                };

                log('📧 Attempting to send test email...');
                log('Service ID: ' + SERVICE_ID);
                log('Template ID: ' + TEMPLATE_ID);
                log('Public Key: ' + PUBLIC_KEY);

                const response = await emailjs.send(
                    SERVICE_ID,
                    TEMPLATE_ID,
                    templateParams,
                    PUBLIC_KEY
                );

                log('✅ Email sent successfully!', 'success');
                log('Response: ' + JSON.stringify(response), 'success');

            } catch (error) {
                log('❌ Error sending email:', 'error');
                log('Error details: ' + JSON.stringify(error), 'error');

                if (error.text) {
                    log('Error message: ' + error.text, 'error');
                }

                if (error.status) {
                    log('HTTP Status: ' + error.status, 'error');
                }

                // Common error interpretations
                if (error.text && error.text.includes('Invalid')) {
                    log('💡 This looks like an invalid Service ID, Template ID, or Public Key', 'error');
                } else if (error.text && error.text.includes('template')) {
                    log('💡 This looks like a template configuration issue', 'error');
                } else if (error.text && error.text.includes('service')) {
                    log('💡 This looks like a service configuration issue', 'error');
                }
            }
        }

        // Run the test when page loads
        window.onload = testEmailJS;
    </script>
</body>
</html>
