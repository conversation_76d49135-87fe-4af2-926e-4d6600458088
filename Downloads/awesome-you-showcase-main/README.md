# Portfolio Website

A modern, responsive portfolio website built with React, TypeScript, and Tailwind CSS.

## Features

- 🎨 Modern and responsive design
- 📱 Mobile-first approach
- 🚀 Fast loading with Vite
- 📧 Contact form with EmailJS integration
- 🎯 Smooth scrolling navigation
- ✨ Interactive animations
- 🎭 Professional UI components with Shadcn/ui

## Tech Stack

- **Frontend**: React 18, TypeScript
- **Styling**: Tailwind CSS, Shadcn/ui
- **Build Tool**: Vite
- **Email Service**: EmailJS
- **Icons**: Lucide React
- **Animations**: CSS animations with Tailwind

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd awesome-you-showcase-main
```

2. Install dependencies:
```bash
npm install
```

3. Set up EmailJS (see EmailJS Setup section below)

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:8080](http://localhost:8080) in your browser

## EmailJS Setup

To enable the contact form, you need to set up EmailJS:

### Step 1: Create EmailJS Account
1. Go to [EmailJS](https://www.emailjs.com/) and create an account
2. Verify your email address

### Step 2: Create Email Service
1. Go to Email Services in your EmailJS dashboard
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the setup instructions
5. Note down your **Service ID**

### Step 3: Create Email Template
1. Go to Email Templates in your EmailJS dashboard
2. Click "Create New Template"
3. Use this template structure:

**Subject**: `New Portfolio Contact - {{subject}}`

**Content**:
```
Hi Tharun,

You received a new message from your portfolio contact form:

From: {{from_name}} ({{from_email}})
Subject: {{subject}}

Message:
{{message}}

Best regards,
Portfolio Contact System
```

4. Make sure to include these variables:
   - `{{from_name}}` - sender's name
   - `{{from_email}}` - sender's email
   - `{{subject}}` - message subject
   - `{{message}}` - message content
   - `{{to_email}}` - your email (optional)
   - `{{reply_to}}` - sender's email for replies

5. Note down your **Template ID**

### Step 4: Get Public Key
1. Go to Account > General in your EmailJS dashboard
2. Find your **Public Key** (User ID)

### Step 5: Update Configuration
Update the EmailJS configuration in `src/components/ContactSection.tsx`:

```typescript
const SERVICE_ID = 'your_service_id_here';
const TEMPLATE_ID = 'your_template_id_here';
const PUBLIC_KEY = 'your_public_key_here';
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── AboutSection.tsx
│   ├── ContactSection.tsx
│   ├── HeroSection.tsx
│   ├── Navbar.tsx
│   ├── ProjectsSection.tsx
│   └── SkillsSection.tsx
├── hooks/              # Custom React hooks
├── lib/                # Utility functions
├── pages/              # Page components
└── main.tsx           # App entry point
```

## Customization

### Colors
The color scheme is defined in `tailwind.config.ts` under the `portfolio` theme:

```typescript
portfolio: {
  blue: '#4F46E5',
  darkBlue: '#4338CA',
  slate: '#0F172A',
  gray: '#F8FAFC',
  accent: '#8B5CF6',
  // ... more colors
}
```

### Content
Update the content in each component file:
- Personal information in `HeroSection.tsx`
- About content in `AboutSection.tsx`
- Skills in `SkillsSection.tsx`
- Projects in `ProjectsSection.tsx`
- Contact info in `ContactSection.tsx`

## Deployment

### Build for Production
```bash
npm run build
```

The built files will be in the `dist/` directory.

### Deploy to Vercel
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Deploy to Netlify
1. Build the project: `npm run build`
2. Upload the `dist/` folder to Netlify

## Troubleshooting

### EmailJS Issues
- Check browser console for detailed error messages
- Verify Service ID, Template ID, and Public Key are correct
- Ensure your email service is properly configured
- Check EmailJS dashboard for usage limits

### Build Issues
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Update dependencies: `npm update`

## License

This project is open source and available under the [MIT License](LICENSE).