# EmailJS Configuration
# Copy this file to .env.local and fill in your actual values

VITE_EMAILJS_SERVICE_ID=your_service_id_here
VITE_EMAILJS_TEMPLATE_ID=your_template_id_here
VITE_EMAILJS_PUBLIC_KEY=your_public_key_here

# Instructions:
# 1. Go to https://www.emailjs.com/ and create an account
# 2. Create a new email service (Gmail, Outlook, etc.)
# 3. Create a new email template with these variables:
#    - {{from_name}} - sender's name
#    - {{from_email}} - sender's email  
#    - {{subject}} - message subject
#    - {{message}} - message content
#    - {{to_email}} - your email
#    - {{reply_to}} - sender's email for replies
# 4. Copy the Service ID, Template ID, and Public Key to .env.local
